/swatmanga
├── public
│   ├── favicon.ico
│   └── assets/
│       ├── logo.svg
│       └── images/…
├── src
│   ├── auth
│   │   ├── AuthContext.tsx           # React Context + provider for auth state (accessToken in memory)
│   │   ├── AuthGuard.tsx             # HOC or component to wrap protected pages
│   │   └── hooks/
│   │       └── useAuth.ts            # login(), logout(), token refresh logic via React Query mutations
│   │
│   ├── components
│   │   ├── atoms
│   │   │   ├── Button.tsx            # Chakra-wrapped, with theme variants
│   │   │   ├── Input.tsx
│   │   │   ├── Heading.tsx
│   │   │   └── Spinner.tsx
│   │   │
│   │   ├── molecules
│   │   │   ├── FormField.tsx         # Input + Label + Error
│   │   │   └── UserAvatar.tsx
│   │   │
│   │   ├── organisms
│   │   │   ├── Navbar.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   └── NotificationBell.tsx
│   │   │
│   │   ├── templates
│   │   │   └── DashboardTemplate.tsx # Navbar + Sidebar + content area
│   │   │
│   │   └── pages                       # “Page-specific” components if needed
│   │       └── LoginPageForm.tsx
│   │
│   ├── features
│   │   ├── auth
│   │   │   ├── login
│   │   │   │   ├── LoginForm.tsx      # uses atoms/molecules
│   │   │   │   └── useLogin.ts        # React Query mutation
│   │   │   └── refresh
│   │   │       └── useRefreshToken.ts
│   │   │
│   │   └── user
│   │       ├── profile
│   │       │   ├── ProfileCard.tsx
│   │       │   └── useUserProfile.ts  # React Query query
│   │       └── settings
│   │           └── useUpdateSettings.ts
│   │
│   ├── hooks
│   │   ├── useAxios.ts                # returns axios instance with interceptors
│   │   └── useIsomorphicLayoutEffect.ts
│   │
│   ├── layouts
│   │   └── AppLayout.tsx             # wraps AuthGuard + ChakraProvider + theme + React Query Provider
│   │
│   ├── lib
│   │   ├── axios.ts                   # createAxiosInstance(), attach JWT, refresh on 401
│   │   └── reactQuery.ts              # queryClient setup, default options
│   │
│   ├── pages
│   │   ├── _app.tsx                   # custom App, wrap providers here
│   │   ├── _document.tsx              # for SEO, meta tags
│   │   ├── index.tsx                  # public landing
│   │   ├── login.tsx                  # Login page, uses LoginForm
│   │   └── dashboard.tsx              # protected via AuthGuard + DashboardTemplate
│   │
│   ├── services
│   │   ├── api.ts                     # base URL, endpoints
│   │   ├── authService.ts             # login(), refreshToken(), logout()
│   │   └── userService.ts             # getProfile(), updateProfile(), etc.
│   │
│   ├── theme
│   │   ├── index.ts                   # extendTheme({ colors, fonts, … })
│   │   └── components
│   │       ├── Button.ts              # custom variants: solid, outline, ghost
│   │       ├── Input.ts
│   │       ├── Card.ts
│   │       └── Badge.ts
│   │
│   ├── types
│   │   ├── auth.ts                    # TokenResponse, Credentials
│   │   └── user.ts                    # UserProfile, Settings
│   │
│   └── utils
│       └── date.ts                    # formatting helpers
│
├── .env.local                         # NEXT_PUBLIC_API_URL, etc.
├── next.config.js
├── tsconfig.json
├── package.json
├── jest.config.js
└── README.md
```

**Highlights and Justification**

* **Atomic Design**: `components/atoms → molecules → organisms → templates` keeps UI bits maximally reusable and maintainable.
* **Auth Isolation**: `auth/` directory contains context, guards, and auth-specific hooks. Tokens live in memory (access) and HTTP-only cookies (refresh).
* **Data Fetching**: React Query in `features/…` folders for co-located hooks (`useLogin.ts`, `useUserProfile.ts`). Default cache and retry rules in `lib/reactQuery.ts`.
* **Axios Interceptors**: Single `axios.ts` exports an instance that attaches access tokens, catches 401s, calls refresh, retries original requests.
* **Chakra Custom Theme**: `theme/index.ts` extends colors/typography; `theme/components/…` defines component variants and supports dark mode out of the box.
* **Skeletons & Loading**: Wrap any data-driven UI (`ProfileCard`, `DataTable`, etc.) in `<Skeleton>` or `<SkeletonText>` matching the final layout.
* **File Organization**:

  * `features/` groups domain logic (auth, user).
  * `services/` centralizes raw API calls.
  * `hooks/` is for generic custom hooks.
  * `layouts/` for top-level layout components.
  * `lib/` for low-level client config and providers.
* **Best Practices**:

  * All code in TypeScript.
  * Accessibility via Chakra’s aria props.
  * Responsive styling with Chakra’s responsive props.
  * Secure cookie usage (`SameSite`, `Secure`, `HttpOnly`).
